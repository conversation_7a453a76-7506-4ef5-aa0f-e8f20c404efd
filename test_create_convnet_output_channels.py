#!/usr/bin/env python3
"""
Test script to verify that create_convnet returns the correct number of output channels.
Based on the test_unified_convnet approach but focused specifically on output channel validation.

This script tests all convnet types (DenseNet, ResNet, VanillaCNN, X3DNet) with both
legacy and unified formats, with special attention to SplitReLU activation which doubles
the output channels.
"""

import torch
import torch.nn as nn
from DataYatesV1.models.factory import create_convnet

def test_output_channels_match(convnet_type, expected_channels, actual_channels, test_name):
    """Helper function to validate output channels match expectations."""
    if expected_channels == actual_channels:
        print(f"  ✓ {test_name}: Expected {expected_channels}, got {actual_channels}")
        return True
    else:
        print(f"  ✗ {test_name}: Expected {expected_channels}, got {actual_channels} - MISMATCH!")
        return False

def test_vanilla_cnn_output_channels():
    """Test VanillaCNN output channel calculations."""
    print("Testing VanillaCNN output channels...")
    
    all_passed = True
    
    # Test 1: Unified format without SplitReLU
    print("\n  Test 1: Unified format, no SplitReLU")
    model, out_channels = create_convnet(
        "vanilla",
        in_channels=6,
        dim=3,
        channels=[16, 32, 24],  # Last layer should have 24 output channels
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "relu"
        }
    )
    expected = 24  # Last layer channels
    all_passed &= test_output_channels_match("vanilla", expected, out_channels, "Unified no SplitReLU")
    
    # Test 2: Unified format with SplitReLU (should double output channels)
    print("\n  Test 2: Unified format, with SplitReLU")
    model, out_channels = create_convnet(
        "vanilla",
        in_channels=6,
        dim=3,
        channels=[16, 32, 24],  # Last layer should have 24 * 2 = 48 output channels
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "splitrelu"  # This doubles the channels
        }
    )
    expected = 24 * 2  # Last layer channels doubled by SplitReLU
    all_passed &= test_output_channels_match("vanilla", expected, out_channels, "Unified with SplitReLU")
    
    # Test 3: Legacy format without SplitReLU
    print("\n  Test 3: Legacy format, no SplitReLU")
    model, out_channels = create_convnet(
        "vanilla",
        in_channels=6,
        dim=3,
        base_channels=32,
        layer_configs=[
            {
                "out_channels": 16,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            },
            {
                "out_channels": 32,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            }
        ]
    )
    expected = 32  # Last layer channels
    all_passed &= test_output_channels_match("vanilla", expected, out_channels, "Legacy no SplitReLU")
    
    # Test 4: Legacy format with SplitReLU
    print("\n  Test 4: Legacy format, with SplitReLU")
    model, out_channels = create_convnet(
        "vanilla",
        in_channels=6,
        dim=3,
        base_channels=32,
        layer_configs=[
            {
                "out_channels": 16,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            },
            {
                "out_channels": 32,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "splitrelu"  # This doubles the channels
            }
        ]
    )
    expected = 32 * 2  # Last layer channels doubled by SplitReLU
    all_passed &= test_output_channels_match("vanilla", expected, out_channels, "Legacy with SplitReLU")
    
    return all_passed

def test_resnet_output_channels():
    """Test ResNet output channel calculations."""
    print("\nTesting ResNet output channels...")
    
    all_passed = True
    
    # Test 1: Unified format without SplitReLU
    print("\n  Test 1: Unified format, no SplitReLU")
    model, out_channels = create_convnet(
        "resnet",
        in_channels=6,
        dim=3,
        channels=[16, 32, 24],  # Last layer should have 24 output channels
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "relu"
        }
    )
    expected = 24  # Last layer channels
    all_passed &= test_output_channels_match("resnet", expected, out_channels, "Unified no SplitReLU")
    
    # Test 2: Unified format with SplitReLU (should double output channels)
    print("\n  Test 2: Unified format, with SplitReLU")
    model, out_channels = create_convnet(
        "resnet",
        in_channels=6,
        dim=3,
        channels=[16, 32, 24],  # Last layer should have 24 * 2 = 48 output channels
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "splitrelu"  # This doubles the channels
        }
    )
    expected = 24 * 2  # Last layer channels doubled by SplitReLU
    all_passed &= test_output_channels_match("resnet", expected, out_channels, "Unified with SplitReLU")
    
    # Test 3: Legacy format without SplitReLU
    print("\n  Test 3: Legacy format, no SplitReLU")
    model, out_channels = create_convnet(
        "resnet",
        in_channels=6,
        dim=3,
        base_channels=32,
        layer_configs=[
            {
                "out_channels": 16,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            },
            {
                "out_channels": 32,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            }
        ]
    )
    expected = 32  # Last layer channels
    all_passed &= test_output_channels_match("resnet", expected, out_channels, "Legacy no SplitReLU")
    
    # Test 4: Legacy format with SplitReLU
    print("\n  Test 4: Legacy format, with SplitReLU")
    model, out_channels = create_convnet(
        "resnet",
        in_channels=6,
        dim=3,
        base_channels=32,
        layer_configs=[
            {
                "out_channels": 16,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "relu"
            },
            {
                "out_channels": 32,
                "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
                "norm_type": "batch",
                "act_type": "splitrelu"  # This doubles the channels
            }
        ]
    )
    expected = 32 * 2  # Last layer channels doubled by SplitReLU
    all_passed &= test_output_channels_match("resnet", expected, out_channels, "Legacy with SplitReLU")
    
    return all_passed

def test_densenet_output_channels():
    """Test DenseNet output channel calculations."""
    print("\nTesting DenseNet output channels...")
    
    all_passed = True
    
    # Test 1: Unified format without SplitReLU
    print("\n  Test 1: Unified format, no SplitReLU")
    model, out_channels = create_convnet(
        "densenet",
        in_channels=6,
        dim=3,
        channels=[8, 16, 12],  # Growth rates: 8, 16, 12
        block_config={
            "conv_params": {"type": "depthwise", "kernel_size": [3, 5, 5], "padding": [1, 2, 2]},
            "norm_type": "rms",
            "act_type": "mish"
        }
    )
    # DenseNet concatenates all feature maps: initial(6) + growth1(8) + growth2(16) + growth3(12) = 42
    expected = 6 + 8 + 16 + 12  # Initial + all growth rates
    all_passed &= test_output_channels_match("densenet", expected, out_channels, "Unified no SplitReLU")
    
    # Test 2: Unified format with SplitReLU (should double each growth rate)
    print("\n  Test 2: Unified format, with SplitReLU")
    model, out_channels = create_convnet(
        "densenet",
        in_channels=6,
        dim=3,
        channels=[8, 16, 12],  # Growth rates: 8*2, 16*2, 12*2 due to SplitReLU
        block_config={
            "conv_params": {"type": "depthwise", "kernel_size": [3, 5, 5], "padding": [1, 2, 2]},
            "norm_type": "rms",
            "act_type": "splitrelu"  # This doubles each block's output channels
        }
    )
    # DenseNet with SplitReLU: initial(6) + growth1(8*2) + growth2(16*2) + growth3(12*2) = 6 + 16 + 32 + 24 = 78
    expected = 6 + (8 * 2) + (16 * 2) + (12 * 2)  # Initial + all doubled growth rates
    all_passed &= test_output_channels_match("densenet", expected, out_channels, "Unified with SplitReLU")
    
    # Test 3: Legacy format without SplitReLU
    print("\n  Test 3: Legacy format, no SplitReLU")
    model, out_channels = create_convnet(
        "densenet",
        in_channels=6,
        dim=3,
        growth_rate=8,
        num_blocks=3,
        block_config={
            "conv_params": {"type": "depthwise", "kernel_size": [3, 5, 5], "padding": [1, 2, 2]},
            "norm_type": "rms",
            "act_type": "mish"
        }
    )
    # DenseNet legacy: initial(6) + growth_rate * num_blocks = 6 + 8*3 = 30
    expected = 6 + (8 * 3)  # Initial + growth_rate * num_blocks
    all_passed &= test_output_channels_match("densenet", expected, out_channels, "Legacy no SplitReLU")
    
    # Test 4: Legacy format with SplitReLU
    print("\n  Test 4: Legacy format, with SplitReLU")
    model, out_channels = create_convnet(
        "densenet",
        in_channels=6,
        dim=3,
        growth_rate=8,
        num_blocks=3,
        block_config={
            "conv_params": {"type": "depthwise", "kernel_size": [3, 5, 5], "padding": [1, 2, 2]},
            "norm_type": "rms",
            "act_type": "splitrelu"  # This doubles each block's output channels
        }
    )
    # DenseNet legacy with SplitReLU: initial(6) + (growth_rate * 2) * num_blocks = 6 + (8*2)*3 = 6 + 48 = 54
    expected = 6 + ((8 * 2) * 3)  # Initial + doubled growth_rate * num_blocks
    all_passed &= test_output_channels_match("densenet", expected, out_channels, "Legacy with SplitReLU")
    
    return all_passed

def test_x3dnet_output_channels():
    """Test X3DNet output channel calculations."""
    print("\nTesting X3DNet output channels...")

    all_passed = True

    # Test 1: Unified format without SplitReLU
    print("\n  Test 1: Unified format, no SplitReLU")
    model, out_channels = create_convnet(
        "x3dnet",
        in_channels=6,
        dim=3,
        channels=[32, 64, 96],  # Last stage should have 96 output channels
        t_kernel=3,
        s_kernel=3,
        exp_ratio=2,
        norm_type='grn',
        act_type='silu'
    )
    expected = 96  # Last stage channels
    all_passed &= test_output_channels_match("x3dnet", expected, out_channels, "Unified no SplitReLU")

    # Test 2: Unified format with SplitReLU (should raise error)
    print("\n  Test 2: Unified format, with SplitReLU (should raise error)")
    try:
        model, out_channels = create_convnet(
            "x3dnet",
            in_channels=6,
            dim=3,
            channels=[32, 64, 96],
            t_kernel=3,
            s_kernel=3,
            exp_ratio=2,
            norm_type='grn',
            act_type='splitrelu'  # This should raise an error
        )
        print(f"  ✗ Unified with SplitReLU: Expected error, but got {out_channels} channels - SHOULD HAVE FAILED!")
        all_passed = False
    except ValueError as e:
        if "SplitReLU" in str(e):
            print(f"  ✓ Unified with SplitReLU: Correctly raised error: {e}")
        else:
            print(f"  ✗ Unified with SplitReLU: Wrong error raised: {e}")
            all_passed = False

    # Test 3: Legacy format without SplitReLU
    print("\n  Test 3: Legacy format, no SplitReLU")
    model, out_channels = create_convnet(
        "x3dnet",
        in_channels=6,
        dim=3,
        depth=[2, 3, 2],
        width=[32, 64, 128],  # Last stage should have 128 output channels
        t_kernel=5,
        s_kernel=3,
        exp_ratio=4,
        norm_type='grn',
        act_type='silu',
        stride_stages=[1, 2, 2]
    )
    expected = 128  # Last stage channels
    all_passed &= test_output_channels_match("x3dnet", expected, out_channels, "Legacy no SplitReLU")

    # Test 4: Legacy format with SplitReLU (should raise error)
    print("\n  Test 4: Legacy format, with SplitReLU (should raise error)")
    try:
        model, out_channels = create_convnet(
            "x3dnet",
            in_channels=6,
            dim=3,
            depth=[2, 3, 2],
            width=[32, 64, 128],
            t_kernel=5,
            s_kernel=3,
            exp_ratio=4,
            norm_type='grn',
            act_type='splitrelu',  # This should raise an error
            stride_stages=[1, 2, 2]
        )
        print(f"  ✗ Legacy with SplitReLU: Expected error, but got {out_channels} channels - SHOULD HAVE FAILED!")
        all_passed = False
    except ValueError as e:
        if "SplitReLU" in str(e):
            print(f"  ✓ Legacy with SplitReLU: Correctly raised error: {e}")
        else:
            print(f"  ✗ Legacy with SplitReLU: Wrong error raised: {e}")
            all_passed = False

    return all_passed

def test_edge_cases():
    """Test edge cases and special scenarios."""
    print("\nTesting edge cases...")

    all_passed = True

    # Test 1: 'none' convnet type
    print("\n  Test 1: 'none' convnet type")
    model, out_channels = create_convnet("none", in_channels=6)
    expected = 6  # Should return input channels unchanged
    all_passed &= test_output_channels_match("none", expected, out_channels, "None convnet")

    # Test 2: Aliases ('conv' -> 'vanilla', 'cnn' -> 'vanilla')
    print("\n  Test 2: 'conv' alias")
    model, out_channels = create_convnet(
        "conv",
        in_channels=6,
        dim=3,
        channels=[16, 32],
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "relu"
        }
    )
    expected = 32  # Last layer channels
    all_passed &= test_output_channels_match("conv", expected, out_channels, "Conv alias")

    print("\n  Test 3: 'cnn' alias")
    model, out_channels = create_convnet(
        "cnn",
        in_channels=6,
        dim=3,
        channels=[16, 32],
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "relu"
        }
    )
    expected = 32  # Last layer channels
    all_passed &= test_output_channels_match("cnn", expected, out_channels, "CNN alias")

    # Test 3: Single channel configurations
    print("\n  Test 4: Single channel configuration")
    model, out_channels = create_convnet(
        "vanilla",
        in_channels=1,
        dim=3,
        channels=[8],  # Single layer
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "relu"
        }
    )
    expected = 8  # Single layer channels
    all_passed &= test_output_channels_match("vanilla", expected, out_channels, "Single channel")

    return all_passed

def verify_actual_output_shape(convnet_type, model, out_channels, test_name):
    """Verify that the actual forward pass output matches the reported output channels."""
    try:
        # Create test input
        x = torch.randn(2, 6, 8, 32, 32)  # Batch=2, Channels=6, Time=8, H=32, W=32

        # Forward pass
        with torch.no_grad():
            y = model(x)

        actual_output_channels = y.shape[1]  # Channel dimension

        if actual_output_channels == out_channels:
            print(f"    ✓ {test_name}: Forward pass confirms {actual_output_channels} channels")
            return True
        else:
            print(f"    ✗ {test_name}: Forward pass shows {actual_output_channels} channels, but get_output_channels() returned {out_channels}")
            return False
    except Exception as e:
        print(f"    ✗ {test_name}: Forward pass failed with error: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive tests with forward pass verification."""
    print("=" * 80)
    print("COMPREHENSIVE CREATE_CONVNET OUTPUT CHANNELS TEST")
    print("=" * 80)

    all_tests_passed = True

    # Test each convnet type
    all_tests_passed &= test_vanilla_cnn_output_channels()
    all_tests_passed &= test_resnet_output_channels()
    all_tests_passed &= test_densenet_output_channels()
    all_tests_passed &= test_x3dnet_output_channels()
    all_tests_passed &= test_edge_cases()

    # Additional verification: Test a few cases with actual forward passes
    print("\n" + "=" * 80)
    print("FORWARD PASS VERIFICATION")
    print("=" * 80)

    # Test ResNet with SplitReLU (suspected issue)
    print("\nVerifying ResNet with SplitReLU forward pass...")
    model, out_channels = create_convnet(
        "resnet",
        in_channels=6,
        dim=3,
        channels=[16, 32],
        block_config={
            "conv_params": {"type": "standard", "kernel_size": [3, 3, 3], "padding": [1, 1, 1]},
            "norm_type": "batch",
            "act_type": "splitrelu"
        }
    )
    all_tests_passed &= verify_actual_output_shape("resnet", model, out_channels, "ResNet SplitReLU")

    # Test DenseNet with SplitReLU
    print("\nVerifying DenseNet with SplitReLU forward pass...")
    model, out_channels = create_convnet(
        "densenet",
        in_channels=6,
        dim=3,
        channels=[8, 16],
        block_config={
            "conv_params": {"type": "depthwise", "kernel_size": [3, 5, 5], "padding": [1, 2, 2]},
            "norm_type": "rms",
            "act_type": "splitrelu"
        }
    )
    all_tests_passed &= verify_actual_output_shape("densenet", model, out_channels, "DenseNet SplitReLU")

    # Test X3DNet with SplitReLU
    print("\nVerifying X3DNet with SplitReLU forward pass...")
    model, out_channels = create_convnet(
        "x3dnet",
        in_channels=6,
        dim=3,
        channels=[32, 64],
        t_kernel=3,
        s_kernel=3,
        exp_ratio=2,
        norm_type='grn',
        act_type='splitrelu'
    )
    all_tests_passed &= verify_actual_output_shape("x3dnet", model, out_channels, "X3DNet SplitReLU")

    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! create_convnet output channels are correct.")
    else:
        print("❌ SOME TESTS FAILED! There are issues with create_convnet output channel calculations.")
        print("\nRecommended actions:")
        print("1. Check ResNet implementation for SplitReLU handling")
        print("2. Verify DenseNet channel accumulation logic")
        print("3. Review X3DNet output channel calculation")
        print("4. Ensure ConvBlock.output_channels property correctly handles SplitReLU")

    return all_tests_passed

if __name__ == "__main__":
    run_comprehensive_test()
