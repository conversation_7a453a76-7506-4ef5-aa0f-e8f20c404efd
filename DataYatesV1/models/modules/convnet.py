# networks.py
import torch
import torch.nn as nn
from torch.utils.checkpoint import checkpoint
from typing import List, Dict, Any, Tu<PERSON>, Union, Optional

from .common import chomp
from .conv_blocks import ConvBlock, ResBlock, X3DUnit
from .norm_act_pool import get_activation_layer

__all__ = ['BaseConvNet', 'VanillaCNN', 'ResNet', 'DenseNet', 'X3DNet']

class BaseConvNet(nn.Module):
    """Base class for configurable convolutional networks."""
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.dim: int = config['dim']
        self.initial_channels: int = config['initial_channels']
        self.base_channels: int = config.get('base_channels', self.initial_channels)
        self.use_checkpointing: bool = config.get('checkpointing', False)
        self.layers = nn.ModuleList()
        self._build_network()

    def _build_network(self): raise NotImplementedError
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        for layer in self.layers:
            if self.use_checkpointing and self.training and isinstance(layer, (ConvBlock, ResBlock)):
                x = checkpoint(layer, x, use_reentrant=False)
            else:
                x = layer(x)
        return x
    def get_output_channels(self) -> int:
        if not self.layers: return self.initial_channels
        last_layer_with_channels = self.initial_channels
        for layer in reversed(self.layers):
            if hasattr(layer, 'output_channels'):
                last_layer_with_channels = layer.output_channels
                break
        return last_layer_with_channels


class VanillaCNN(BaseConvNet):
    """Sequential CNN built from ConvBlocks."""
    def _build_network(self):
        current_channels = self.initial_channels

        # Support both new unified format and legacy format
        if 'channels' in self.config and 'block_config' in self.config:
            # New unified format: channels list + block_config
            channels = self.config['channels']
            block_cfg_base = self.config['block_config'].copy()
            block_cfg_base['dim'] = self.dim

            for i, out_channels in enumerate(channels):
                block = ConvBlock(
                    in_channels=current_channels,
                    out_channels=out_channels,
                    **block_cfg_base
                )
                self.layers.append(block)
                current_channels = block.output_channels
        else:
            # Legacy format: layer_configs
            for i, block_cfg in enumerate(self.config['layer_configs']):
                cfg = block_cfg.copy()
                cfg['dim'] = self.dim
                out_ch_conv = cfg.pop('out_channels', None) or \
                              int(self.base_channels * cfg.pop('channel_multiplier', 1))

                # No special handling needed for normalization parameters
                # ConvBlock will handle normalization internally

                block = ConvBlock(in_channels=current_channels, out_channels=out_ch_conv, **cfg)
                self.layers.append(block)
                current_channels = block.output_channels

# ResBlock helper moved to blocks.py for better organization if preferred,
# but keeping it here is also fine if it's only used by ResNet.
# Let's assume it's defined in blocks.py as per the previous update.
# from .blocks import ResBlock

class ResNet(BaseConvNet):
    """ResNet built from ConvBlocks."""
    def _build_network(self):
        current_channels = self.initial_channels

        # Optional Stem
        if 'stem_config' in self.config:
            stem_cfg = self.config['stem_config'].copy()
            stem_cfg['dim'] = self.dim
            out_ch_stem = stem_cfg.pop('out_channels')
            # No special handling needed for normalization parameters
            # ConvBlock will handle normalization internally
            stem = ConvBlock(in_channels=current_channels, out_channels=out_ch_stem, **stem_cfg)
            self.layers.append(stem)
            current_channels = stem.output_channels

        # Support both new unified format and legacy format
        if 'channels' in self.config and 'block_config' in self.config:
            # New unified format: channels list + block_config
            channels = self.config['channels']
            block_cfg_base = self.config['block_config'].copy()
            block_cfg_base['dim'] = self.dim

            for out_channels in channels:
                # Remove out_channels from block config if present to avoid conflicts
                cfg = block_cfg_base.copy()
                cfg.pop('out_channels', None)
                cfg.pop('channel_multiplier', None)

                main_block = ConvBlock(in_channels=current_channels, out_channels=out_channels, **cfg)
                main_block_out_ch = main_block.output_channels

                self._add_resnet_block(main_block, current_channels, main_block_out_ch, cfg)
                current_channels = main_block_out_ch
        else:
            # Legacy format: layer_configs
            for block_cfg in self.config['layer_configs']:
                cfg = block_cfg.copy()
                cfg['dim'] = self.dim
                out_ch_conv = cfg.pop('out_channels', None) or \
                              int(self.base_channels * cfg.pop('channel_multiplier', 1))

                # No special handling needed for normalization parameters
                # ConvBlock will handle normalization internally

                main_block = ConvBlock(in_channels=current_channels, out_channels=out_ch_conv, **cfg)
                main_block_out_ch = main_block.output_channels

                self._add_resnet_block(main_block, current_channels, main_block_out_ch, cfg)
                current_channels = main_block_out_ch

    def _add_resnet_block(self, main_block, current_channels, main_block_out_ch, block_cfg=None):
        """Add a ResNet block with shortcut connection."""
        # Enhanced shortcut connection handling
        needs_projection = False

        # Infer stride from main_block's conv_params
        if block_cfg:
            block_conv_params = block_cfg.get('conv_params', {})
        else:
            # For new unified format, get from main_block config
            block_conv_params = getattr(main_block, 'config', {}).get('conv_params', {})
        conv_stride_cfg = block_conv_params.get('stride', 1)

        # Check if stride requires projection
        if isinstance(conv_stride_cfg, tuple):
            needs_projection = any(s > 1 for s in conv_stride_cfg)
        else:
            needs_projection = conv_stride_cfg > 1

        # Check if channel dimensions require projection
        if current_channels != main_block_out_ch:
            needs_projection = True

        # Check if projection is enabled in config
        enable_projection = self.config.get('resnet_shortcut_projection', True)

        # Default to identity shortcut
        shortcut: nn.Module = nn.Identity()

        # Create projection shortcut if needed and enabled
        if needs_projection and enable_projection:
            ShortcutConv = nn.Conv2d if self.dim == 2 else nn.Conv3d

            # Get shortcut parameters from config
            sc_params = self.config.get('resnet_shortcut_params', {})
            kernel_size = sc_params.get('kernel_size', 1)
            bias = sc_params.get('bias', False)

            # Shortcut norm can be configured
            sc_norm_type = self.config.get('resnet_shortcut_norm_type')

            # Get shortcut normalization parameters
            sc_norm_params = self.config.get('resnet_shortcut_norm_params', {})

            from .norm_act_pool import get_norm_layer
            sc_norm = get_norm_layer(sc_norm_type, main_block_out_ch, self.dim, sc_norm_params)

            # Create the projection shortcut
            shortcut = nn.Sequential(
                ShortcutConv(
                    current_channels,
                    main_block_out_ch,
                    kernel_size=kernel_size,
                    stride=conv_stride_cfg,
                    bias=bias
                ),
                sc_norm
            )

        post_add_act = get_activation_layer(self.config.get('resnet_post_add_activation'))
        self.layers.append(ResBlock(main_block, shortcut, post_add_act))


class DenseNet(BaseConvNet):
    """
    DenseNet built from ConvBlocks.

    In a DenseNet, each layer receives the feature maps from all preceding layers.
    The number of input channels for each layer increases as we go deeper in the network.
    """
    def _build_network(self, verbose=False):
        # Initialize with the input channels
        current_channels = self.initial_channels
        if verbose:
            print(f"\nBuilding DenseNet with initial channels: {current_channels}")

        # Get configuration parameters - support both old and new formats
        if 'channels' in self.config:
            # New unified format: explicit channel list
            channels = self.config['channels']
            num_blocks = len(channels)
            if verbose:
                print(f"Using new channels format: {channels}")
        else:
            # Legacy format: growth_rate + num_blocks
            growth_rate = self.config['growth_rate']
            num_blocks = self.config['num_blocks']
            channels = [growth_rate] * num_blocks
            if verbose:
                print(f"Using legacy growth_rate format: {growth_rate} x {num_blocks}")

        block_cfg_base = self.config['block_config'].copy()
        if verbose:
            print(f"Base block config: {block_cfg_base}")
        block_cfg_base['dim'] = self.dim

        # Check if we're using SplitReLU which would double the output channels
        uses_split_relu = block_cfg_base.get('act_type', '') == 'splitrelu'
        if uses_split_relu and verbose:
            print(f"DenseNet using SplitReLU - output channels will be doubled")

        # Store channel counts for each layer for debugging and validation
        self.input_channels_per_block = [current_channels]

        # Create each block with the correct number of input channels
        for i in range(num_blocks):
            if verbose:
                print(f"Creating DenseNet block {i} with input channels: {current_channels}")

            # Create a block that takes all previous feature maps as input
            block = ConvBlock(
                in_channels=current_channels,
                out_channels=channels[i],  # Use channels[i] instead of growth_rate
                **block_cfg_base
            )
            self.layers.append(block)

            # Check if this block uses SplitReLU
            block_uses_split_relu = hasattr(block, '_is_split_relu') and block._is_split_relu

            # Get the actual output channels from the block
            block_output_channels = block.output_channels
            

            # Update the channel count for the next block
            # The next block will receive all previous feature maps plus this block's output
            current_channels += block_output_channels
            if verbose:
                print(f"  Block {i} output channels: {block_output_channels}")
                print(f"  Block {i} uses SplitReLU: {block_uses_split_relu}")
                print(f"  Total channels after block {i}: {current_channels}")

            # Store the input channel count for the next block
            self.input_channels_per_block.append(current_channels)

        # The final output channels is the sum of initial channels and all growth
        self._final_out_channels = current_channels
        if verbose:
            print(f"DenseNet final output channels: {self._final_out_channels}")

    # We've moved the functionality of _run_one_block directly into the forward method
    # to avoid issues with checkpointing

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the DenseNet.

        Args:
            x: Input tensor

        Returns:
            Output tensor with all feature maps concatenated
        """
        # Start with just the input as the first feature map
        feature_maps = [x]

        # Process through each block
        for i in range(len(self.layers)):
            # Get the current block
            block = self.layers[i]

            # Find the minimum spatial dimensions across all input feature maps
            min_spatial_shape = feature_maps[0].shape[-2:]
            for f in feature_maps[1:]:
                min_spatial_shape = tuple(min(s1, s2) for s1, s2 in zip(min_spatial_shape, f.shape[-2:]))

            # Crop all inputs to the minimum spatial dimensions
            chomped_inputs = [chomp(f, min_spatial_shape) for f in feature_maps]

            # Concatenate all inputs along the channel dimension
            x_cat = torch.cat(chomped_inputs, dim=1)

            # Run the block, with checkpointing if enabled
            if self.use_checkpointing and self.training:
                # Use checkpoint directly on the block with the concatenated input
                # This avoids the issue with checkpointing a function that takes a list
                block_out = checkpoint(
                    block,
                    x_cat,
                    use_reentrant=False,
                    preserve_rng_state=True
                )
            else:
                # Direct computation
                block_out = block(x_cat)

            # Add this block's output to the feature maps
            feature_maps.append(block_out)

        # Final concatenation of all feature maps
        # First crop all to the same spatial dimensions
        final_spatial_shape = feature_maps[-1].shape[-2:]
        final_chomped_maps = [chomp(f, final_spatial_shape) for f in feature_maps]

        # Then concatenate along the channel dimension
        return torch.cat(final_chomped_maps, dim=1)

    def get_output_channels(self) -> int:
        """Get the number of output channels from the network."""
        return self._final_out_channels

class X3DNet(BaseConvNet):
    """
    Progressive-scaling X3D backbone.
    `config` keys
        depth:  list[int]   # layers per stage   e.g. [2, 5, 5]
        width:  list[int]   # out-channels list  e.g. [64, 128, 256]
        t_kernel: int       # temporal kernel for the first stage (dilates later)
        s_kernel: int       # spatial kernel (usually 3 or 5)
        exp_ratio: int      # MLP expansion ratio
        norm_type: str      # 'grn', 'group', 'batch', ...
        act_type: str
        stride_stages: list[int]  # temporal strides per stage, usually [1,2,2]
    """
    def _build_network(self):
        cfg = self.config
        C_in = self.initial_channels

        # Support both new unified format and legacy format
        if 'channels' in cfg:
            # New unified format: channels list
            channels = cfg['channels']
            # For unified format, assume 1 block per stage unless specified
            depth = cfg.get('depth', [1] * len(channels))
            width = channels
            if len(depth) == 1 and len(channels) > 1:
                # If single depth value given, replicate for all stages
                depth = depth * len(channels)
        else:
            # Legacy format: depth + width
            depth = cfg['depth']      # e.g. [2, 5, 5]
            width = cfg['width']      # e.g. [64, 128, 256]

        assert len(depth) == len(width)

        t_kernel = cfg.get('t_kernel', 5)
        s_kernel_base = cfg.get('s_kernel', 3)
        exp = cfg.get('exp_ratio', 4)
        norm = cfg.get('norm_type', 'grn')
        act  = cfg.get('act_type', 'silu')
        t_stride = cfg.get('stride_stages', [1]*len(depth))

        # Dropout parameters
        dropout = cfg.get('dropout', 0.0)
        stochastic_depth = cfg.get('stochastic_depth', 0.0)

        for stage, (d, C_out, s) in enumerate(zip(depth, width, t_stride)):
            
            # first block in the stage may down-sample temporally or spatially
            if cfg.get('lite_lk', False) and stage % cfg.get('lk_every', 2) == 1:
                s_kernel = 7         
                t_kernel = 3
            else:
                s_kernel = s_kernel_base

            first_block = X3DUnit(C_in, C_out, t_kernel, s_kernel,
                                  exp, norm, act, dim=self.dim,
                                  dropout=dropout, stochastic_depth=stochastic_depth)
            if s > 1:
                # Access the underlying conv layer properly based on conv type
                conv_layer = first_block.pre_temporal.components['conv']
                if hasattr(conv_layer, 'conv'):  # StandardConv
                    conv_layer.conv.stride = (s, 1, 1)
                elif hasattr(conv_layer, 'depthwise'):  # DepthwiseConv
                    conv_layer.depthwise.stride = (s, 1, 1)
                else:
                    # Fallback: try to set stride directly
                    conv_layer.stride = (s, 1, 1)
            self.layers.append(first_block)
            C_prev = C_out
            # remaining blocks (identity stride)
            for _ in range(d - 1):
                blk = X3DUnit(C_prev, C_prev, t_kernel, s_kernel,
                              exp, norm, act, dim=self.dim,
                              dropout=dropout, stochastic_depth=stochastic_depth)
                self.layers.append(blk)
            C_in = C_prev

        # Store the final output channels for get_output_channels()
        self._final_out_channels = C_in

    def get_output_channels(self) -> int:
        """Get the number of output channels from the network."""
        return self._final_out_channels

CONVNETS = {'vanilla': VanillaCNN,
            'cnn': VanillaCNN,
            'resnet': ResNet,
            'densenet': DenseNet,
            'x3d': X3DNet, 
            'x3dnet': X3DNet}

def build_model(config: Dict[str, Any]) -> nn.Module:
    """Builds a CNN model based on config."""
    model_type = config.get('model_type', 'vanilla').lower()
    if model_type in ['vanilla', 'cnn']: return VanillaCNN(config)
    if model_type == 'resnet': return ResNet(config)
    if model_type == 'densenet': return DenseNet(config)
    if model_type in ['x3d', 'x3dnet']: return X3DNet(config)
    raise ValueError(f"Unknown model type: '{model_type}'.")