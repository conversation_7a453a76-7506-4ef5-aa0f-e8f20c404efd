"""
Data loading utilities for neural data analysis.

This module provides functions for loading, preprocessing, and organizing neural datasets
for model training and analysis. It handles dataset loading, time embedding, train/validation
splitting, and calculation of spike-triggered statistics.

The module supports:
- Loading multiple dataset types and combining them
- Creating reproducible train/validation splits based on trials
- Calculating and caching spike-triggered averages and second moments
- Preprocessing datasets with custom functions
"""
import torch
import numpy as np
from .datasets import DictDataset, CombinedEmbeddedDataset
from .filtering import get_valid_dfs
from .splitting import split_inds_by_trial
from .transforms import make_pipeline
from .datafilters import make_datafilter_pipeline
from ..general import ensure_tensor
from ..io import get_session
from ..rf import calc_sta
from typing import Dict, Any, List
import yaml

def get_embedded_datasets(sess, types=None, keys_lags=None, train_val_split=None, cids=None, seed=1002, pre_func=None, **kwargs):
    """
    Create train and validation datasets from multiple dataset types with time embedding.

    This function loads multiple datasets, applies preprocessing, filters valid frames,
    splits them into training and validation sets based on trials, and combines them
    into embedded datasets ready for model training.

    Parameters
    ----------
    sess : Session object
        Session object containing path information
    types : list of str or dict datasets
        List of dataset types to load (e.g., ['gaborium', 'backimage'])
        or a list of DictDataset objects
    keys_lags : dict
        Dictionary mapping dataset keys to lag values for time embedding
        Example: {'robs': 0, 'stim': np.arange(10)}
    train_val_split : float
        Fraction of data to use for training (between 0 and 1)
    cids : array-like, optional
        Cell IDs to include. If None, all cells are included
    seed : int, optional
        Random seed for reproducible train/validation splits, default=1002
    pre_func : callable, optional
        Function to apply to each dataset after loading

    Returns
    -------
    train_dset : CombinedEmbeddedDataset
        Combined dataset for training
    val_dset : CombinedEmbeddedDataset
        Combined dataset for validation
    """
    # Determine maximum number of lags needed based on keys_lags
    n_lags = np.max([np.max(keys_lags[k]) for k in keys_lags])

    # Default preprocessing function if none provided
    if pre_func is None:
        def pre_func(x):
            # Normalize stimulus to [-0.5, 0.5] range
            x['stim'] = (x['stim'].float() - 127) / 255
            # Generate valid frame mask based on trial boundaries and DPI validity
            x['dfs'] = get_valid_dfs(x, n_lags)
            return x

    # Load and preprocess each dataset type
    dsets = []
    for dset_type in types:
        if isinstance(dset_type, DictDataset):
            dset = dset_type
        else:
            # Load dataset from file
            dset = DictDataset.load(sess.sess_dir / 'shifter' / f'{dset_type}_shifted.dset')

        # Apply preprocessing
        dset = pre_func(dset)
        
        # Filter by cell IDs if specified
        if cids is not None:
            dset['robs'] = dset['robs'][:,cids]
        dsets.append(dset)

    # Get indices of valid frames for each dataset
    dset_inds = [dset['dfs'].squeeze().nonzero(as_tuple=True)[0] for dset in dsets]

    # Print dataset statistics
    for iD, dset in enumerate(dsets):
        print(f'{types[iD]} dataset size: {len(dset)} / {len(dset_inds[iD])} ({len(dset_inds[iD])/len(dset)*100:.2f}%)')

    # Split indices into training and validation sets by trial
    train_inds, val_inds = [], []
    for iD, dset in enumerate(dsets):
        train_inds_, val_inds_ = split_inds_by_trial(dset, dset_inds[iD], train_val_split, seed)
        train_inds.append(train_inds_)
        val_inds.append(val_inds_)

    # Create combined embedded datasets for training and validation
    train_dset = CombinedEmbeddedDataset(dsets, train_inds, keys_lags)
    val_dset = CombinedEmbeddedDataset(dsets, val_inds, keys_lags)

    return train_dset, val_dset

def get_gaborium_sta_ste(sess, n_lags, cids=None):
    """
    Calculate or load cached spike-triggered averages (STAs) and spike-triggered
    second moments (STEs) for gaborium stimulus data.

    This function first checks if cached STAs/STEs exist and have sufficient lags.
    If so, it loads them from cache. Otherwise, it calculates them from the raw data
    and saves them to cache for future use.

    Parameters
    ----------
    sess : Session object
        Session object containing path information
    n_lags : int
        Number of time lags to calculate STAs/STEs for
    cids : array-like, optional
        Cell IDs to include. If None, all cells are included

    Returns
    -------
    stas : numpy.ndarray
        Spike-triggered averages with shape (n_cells, n_lags, n_y, n_x)
    stes : numpy.ndarray
        Spike-triggered second moments with shape (n_cells, n_lags, n_y, n_x)
    """
    # Verify that the dataset exists
    assert (sess.sess_dir / 'shifter' / 'gaborium_shifted.dset').exists()

    # Define cache file path
    cache_dir = sess.sess_dir / 'shifter' / 'gaborium_sta_ste.npy'

    # Try to load from cache if it exists
    if cache_dir.exists():
        stas, stes = np.load(cache_dir, allow_pickle=True)
        n_lags_cached = stas.shape[1]

        # If cached data has enough lags, use it
        if n_lags_cached >= n_lags:
            if cids is None:
                cids = np.arange(stas.shape[0])

            # Return requested subset of lags and cells
            return stas[cids][:,:n_lags], stes[cids][:,:n_lags]
        else:
            print(f'Cached STAs/STEs have {n_lags_cached} lags, but {n_lags} were requested. Recalculating...')
    else:
        print('Cached STAs/STEs not found. Calculating...')

    # Load and preprocess the dataset
    dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
    dset['stim'] = dset['stim'].float()
    # Normalize stimulus (mean-centered)
    dset['stim'] = (dset['stim'] - dset['stim'].mean()) / 255
    # Generate valid frame mask
    dset['dfs'] = get_valid_dfs(dset, n_lags)

    # Calculate spike-triggered averages (STAs)
    stas = calc_sta(dset['stim'].detach().cpu(),
                   dset['robs'].cpu(),
                   range(n_lags),
                   dfs=dset['dfs'].cpu().squeeze(),
                   progress=True).cpu().squeeze().numpy()

    # Calculate spike-triggered second moments (STEs)
    # Uses squared stimulus values via stim_modifier
    stes = calc_sta(dset['stim'].detach().cpu(),
                   dset['robs'].cpu(),
                   range(n_lags),
                   dfs=dset['dfs'].cpu().squeeze(),
                   stim_modifier=lambda x: x**2,
                   progress=True).cpu().squeeze().numpy()

    # Save results to cache for future use
    try:
        np.save(cache_dir, [stas, stes])
        print(f'STAs/STEs saved to cache: {cache_dir}')
    except Exception as e:
        print(f'Failed to save STAs/STEs to cache: {e}')

    # Filter by cell IDs if specified
    if cids is not None:
        stas = stas[cids]
        stes = stes[cids]

    return stas, stes




# ──────────────────────────────────────────────────────────────────────────────
# 3.  Prepare_data
# ──────────────────────────────────────────────────────────────────────────────
def prepare_data(dataset_config: Dict[str, Any]):
    """
    Extended prepare_data that supports `transforms:` and `datafilters:` blocks with preprocessing.

    Parameters
    ----------
    dataset_config : dict
        Parsed YAML config (already loaded via yaml.safe_load).

    Returns
    -------
    train_dset, val_dset, dataset_config  (unchanged downstream interface)
    """
    print("\nPreparing data (with preprocessing)…")

    # check if dataset_config is a path
    if isinstance(dataset_config, str):
        with open(dataset_config, 'r') as f:
            dataset_config = yaml.safe_load(f)

    # -- unpack ----------------------------------------------------------------
    sess_name  = dataset_config["session"]
    dset_types = dataset_config["types"]
    transforms  = dataset_config.get("transforms", {})
    datafilters = dataset_config.get("datafilters", {})
    keys_lags  = dataset_config["keys_lags"]

    sess = get_session(*sess_name.split("_"))

    # -------------------------------------------------------------------------
    # Build transform specs once
    # -------------------------------------------------------------------------
    transform_specs = {}
    for var_name, spec in transforms.items():
        pipeline = make_pipeline(spec.get("ops", []))
        transform_specs[var_name] = dict(
            source     = spec.get("source", var_name),
            pipeline   = pipeline,
            expose_as  = spec.get("expose_as", var_name),
        )

        # Merge any per-variable keys_lags into the master dict
        if "keys_lags" in spec:
            keys_lags[spec["expose_as"]] = spec["keys_lags"]

    # -------------------------------------------------------------------------
    # Build datafilter specs once
    # -------------------------------------------------------------------------
    datafilter_specs = {}
    for var_name, spec in datafilters.items():
        pipeline = make_datafilter_pipeline(spec.get("ops", []))
        datafilter_specs[var_name] = dict(
            pipeline   = pipeline,
            expose_as  = spec.get("expose_as", var_name),
        )

        # Merge any per-variable keys_lags into the master dict
        if "keys_lags" in spec:
            keys_lags[spec["expose_as"]] = spec["keys_lags"]

    # Check if datafilters are specified, warn if not
    if not datafilters:
        print("WARNING: No datafilters specified in config. This may lead to invalid samples being included in training.")

    # -------------------------------------------------------------------------
    # Load each DictDataset, run transforms and datafilters in-place, and stash
    # -------------------------------------------------------------------------
    n_lags = dataset_config.get("n_lags", np.max([np.max(keys_lags[k]) for k in keys_lags]))

    preprocessed_dsets = []
    for dt in dset_types:
        dset_path = sess.sess_dir / "shifter" / f"{dt}_shifted.dset"
        dset = DictDataset.load(dset_path)
        dset.metadata['name'] = dt # add name to metadata for later identification
        dset.metadata['sess'] = sess

        # -------------------------------------------------------------
        # Apply datafilter pipelines
        # -------------------------------------------------------------
        if datafilter_specs:
            for var_name, spec in datafilter_specs.items():
                expose_as = spec["expose_as"]
                # print(f"Applying datafilter → {expose_as}")
                mask_tensor = spec["pipeline"](dset)
                dset[expose_as] = mask_tensor
        else:
            # Fallback to old behavior if no datafilters specified
            dset['dfs'] = get_valid_dfs(dset, n_lags)

        # -------------------------------------------------------------
        # Apply transform pipelines
        # -------------------------------------------------------------
        for var_name, spec in transform_specs.items():
            src_key   = spec["source"]
            expose_as = spec["expose_as"]
            # print(f"Transforming {src_key} → {expose_as}")
            data_tensor = ensure_tensor(dset[src_key])   # → torch.Tensor
            data_tensor = spec["pipeline"](data_tensor)
            # print(f"{expose_as} shape: {data_tensor.shape}")
            dset[expose_as] = data_tensor

        preprocessed_dsets.append(dset)

    # -------------------------------------------------------------------------
    # Combine datasets
    # -------------------------------------------------------------------------
    train_dset, val_dset = get_embedded_datasets(
        sess,
        types            = preprocessed_dsets,           # pass in the preprocessed datasets
        keys_lags        = keys_lags,
        train_val_split  = dataset_config["train_val_split"],
        cids             = dataset_config.get("cids", None),
        seed             = dataset_config.get("seed", 1002),
        pre_func         = lambda x: x,          # preprocessing already done
    )

    print(f"Train size: {len(train_dset)} samples | "
          f"Val size: {len(val_dset)} samples")

    # IMPORTANT: pass behaviour feature dim back to model yaml --------------
    beh_keys = [v["expose_as"] for v in transform_specs.values()
                if v["expose_as"] == "behavior"]
    if beh_keys:
        # assume they were concatenated along last dim already
        sample = train_dset[0]["behavior"]
        dataset_config["behavior_dim"] = sample.shape[-1]

    return train_dset, val_dset, dataset_config


def prepare_multidataset_data(dataset_configs: List[Dict[str, Any]], use_fp16: bool = False):
    """
    Prepare data for multidataset training.

    Args:
        dataset_configs: List of dataset configuration dictionaries
        use_fp16: Whether to cast stimulus data to FP16 for memory efficiency

    Returns:
        Tuple of (train_datasets_dict, val_datasets_dict, updated_dataset_configs)
    """
    print(f"\nPreparing multidataset data for {len(dataset_configs)} datasets...")

    train_datasets = {}
    val_datasets = {}
    updated_configs = []

    for i, dataset_config in enumerate(dataset_configs):
        print(f"\nPreparing dataset {i}...")

        # Prepare individual dataset
        train_dset, val_dset, updated_config = prepare_data(dataset_config)

        # Cast stimulus data to FP16 if requested (keep targets in FP32)
        if use_fp16:
            print(f"Casting dataset {i} stimulus data to FP16...")
            train_dset = train_dset.cast(torch.float16)
            val_dset = val_dset.cast(torch.float16)

        # Store with dataset index as key
        dataset_name = f"dataset_{i}"
        train_datasets[dataset_name] = train_dset
        val_datasets[dataset_name] = val_dset
        updated_configs.append(updated_config)

        print(f"Dataset {i}: {len(train_dset)} train, {len(val_dset)} val samples")

    print(f"\nMultidataset preparation complete!")
    return train_datasets, val_datasets, updated_configs
