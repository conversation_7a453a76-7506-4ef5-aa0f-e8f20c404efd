# V1 model configuration with eye position modulation
# This model uses a DA frontend -> DenseNet -> flattened -> GRU -> linear readout
# with MLP modulator output concatenated with the flattened DenseNet output
model_type: v1

# Model dimensions
height: 32
width: 32
sampling_rate: 240
initial_input_channels: 5 # must match num cosines in stim embed

# Frontend configuration
frontend:
  type: none
  params: {}

# Convnet configuration
convnet:
  type: densenet
  params:
    growth_rate: 8
    num_blocks: 3
    dim: 3
    checkpointing: true
    block_config:
      conv_params:
        type: depthwise
        dim: 3
        kernel_size: [1, 5, 5]
        padding: [0,0,0] # or what?
      norm_type: rms
      act_type: mish
      pool_params: {}

# Modulator configuration
modulator:
  type: none
  params: {}

# # Recurrent configuration
# recurrent:
#   type: convgru
#   params:
#     hidden_dim: 128
#     kernel_size: 3

# Recurrent configuration
recurrent:
  type: none
  params: {}

# Readout configuration
readout:
  type: gaussian
  params:
    n_units: 8
    bias: true
    initial_std: 5.0