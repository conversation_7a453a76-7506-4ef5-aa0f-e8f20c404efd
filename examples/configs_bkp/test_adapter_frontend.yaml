# Example configuration using the new AffineAdapter frontend
# This model uses an AffineAdapter frontend -> DenseNet -> GRU -> Gaussian readout
model_type: v1

# Model dimensions
height: 32
width: 32
sampling_rate: 240

# Frontend configuration - using the new adapter frontend
frontend:
  type: adapter
  params:
    init_sigma: 1.5        # Initial blur sigma
    grid_size: 25          # Output spatial dimensions will be 25x25
    transform: scale       # Use 'scale' or 'affine' transformation

# Convnet configuration
convnet:
  type: densenet
  params:
    growth_rate: 8
    num_blocks: 3
    dim: 3
    checkpointing: true
    block_config:
      conv_params:
        type: depthwise
        dim: 3
        kernel_size: [3, 5, 5]
        padding: [1, 2, 2]
      norm_type: rms
      act_type: mish
      pool_params: {}

# Modulator configuration
modulator:
  type: none
  params: {}

# Recurrent configuration
recurrent:
  type: convgru
  params:
    hidden_dim: 32

# Readout configuration
readout:
  type: gaussian
  params:
    n_units: 100
